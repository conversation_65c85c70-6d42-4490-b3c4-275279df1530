import { User } from 'firebase/auth';
import { getValidAccessToken } from '../auth/token-manager';

// API base URL - this should be configured based on environment
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  requireAuth?: boolean;
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  // Make authenticated API request
  async request<T = any>(
    endpoint: string,
    options: ApiRequestOptions = {},
    user?: User | null
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      requireAuth = true,
    } = options;

    try {
      const url = `${this.baseURL}${endpoint}`;
      
      // Prepare headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers,
      };

      // Add authorization header if authentication is required
      if (requireAuth && user) {
        const token = await getValidAccessToken(user);
        if (token) {
          requestHeaders.Authorization = `Bearer ${token}`;
        } else {
          throw new Error('No valid access token available');
        }
      }

      // Prepare request options
      const requestOptions: RequestInit = {
        method,
        headers: requestHeaders,
      };

      // Add body for non-GET requests
      if (body && method !== 'GET') {
        requestOptions.body = JSON.stringify(body);
      }

      // Make the request
      const response = await fetch(url, requestOptions);
      
      // Parse response
      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: responseData.error || `HTTP ${response.status}: ${response.statusText}`,
          message: responseData.message,
        };
      }

      return {
        success: true,
        data: responseData.data || responseData,
        message: responseData.message,
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, user?: User | null): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' }, user);
  }

  async post<T = any>(endpoint: string, body: any, user?: User | null): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body }, user);
  }

  async put<T = any>(endpoint: string, body: any, user?: User | null): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body }, user);
  }

  async patch<T = any>(endpoint: string, body: any, user?: User | null): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body }, user);
  }

  async delete<T = any>(endpoint: string, user?: User | null): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' }, user);
  }

  // Upload file with authentication
  async uploadFile(
    endpoint: string,
    file: File,
    user?: User | null,
    additionalData?: Record<string, any>
  ): Promise<ApiResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // Add additional data if provided
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }

      const headers: Record<string, string> = {};

      // Add authorization header if user is provided
      if (user) {
        const token = await getValidAccessToken(user);
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        } else {
          throw new Error('No valid access token available');
        }
      }

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData,
      });

      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: responseData.error || `HTTP ${response.status}: ${response.statusText}`,
          message: responseData.message,
        };
      }

      return {
        success: true,
        data: responseData.data || responseData,
        message: responseData.message,
      };
    } catch (error) {
      console.error('File upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export class for custom instances
export default ApiClient;
