import { User } from 'firebase/auth';
import Cookies from 'js-cookie';

// Token storage keys
const ACCESS_TOKEN_KEY = 'firebase_access_token';
const REFRESH_TOKEN_KEY = 'firebase_refresh_token';
const USER_DATA_KEY = 'user_data';

// Cookie options for secure storage
const COOKIE_OPTIONS = {
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  expires: 7, // 7 days
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  expires: 30, // 30 days for refresh token
};

export interface StoredUserData {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

// Store Firebase tokens securely
export const storeTokens = async (user: User): Promise<void> => {
  try {
    // Get the ID token
    const idToken = await user.getIdToken();
    
    // Store access token
    Cookies.set(ACCESS_TOKEN_KEY, idToken, COOKIE_OPTIONS);
    
    // Store refresh token (if available)
    if (user.refreshToken) {
      Cookies.set(REFRESH_TOKEN_KEY, user.refreshToken, REFRESH_COOKIE_OPTIONS);
    }
    
    // Store user data
    const userData: StoredUserData = {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
    };
    
    Cookies.set(USER_DATA_KEY, JSON.stringify(userData), COOKIE_OPTIONS);
  } catch (error) {
    console.error('Error storing tokens:', error);
    throw error;
  }
};

// Get stored access token
export const getAccessToken = (): string | null => {
  return Cookies.get(ACCESS_TOKEN_KEY) || null;
};

// Get stored refresh token
export const getRefreshToken = (): string | null => {
  return Cookies.get(REFRESH_TOKEN_KEY) || null;
};

// Get stored user data
export const getStoredUserData = (): StoredUserData | null => {
  try {
    const userData = Cookies.get(USER_DATA_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing stored user data:', error);
    return null;
  }
};

// Clear all stored tokens and user data
export const clearTokens = (): void => {
  Cookies.remove(ACCESS_TOKEN_KEY);
  Cookies.remove(REFRESH_TOKEN_KEY);
  Cookies.remove(USER_DATA_KEY);
};

// Refresh access token
export const refreshAccessToken = async (user: User): Promise<string> => {
  try {
    // Force refresh the token
    const newToken = await user.getIdToken(true);
    
    // Update stored token
    Cookies.set(ACCESS_TOKEN_KEY, newToken, COOKIE_OPTIONS);
    
    return newToken;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    // Clear tokens if refresh fails
    clearTokens();
    throw error;
  }
};

// Check if token is expired (basic check)
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    // If we can't parse the token, consider it expired
    return true;
  }
};

// Get valid access token (refresh if needed)
export const getValidAccessToken = async (user: User | null): Promise<string | null> => {
  if (!user) return null;
  
  const storedToken = getAccessToken();
  
  if (!storedToken || isTokenExpired(storedToken)) {
    try {
      return await refreshAccessToken(user);
    } catch (error) {
      console.error('Failed to refresh token:', error);
      return null;
    }
  }
  
  return storedToken;
};
