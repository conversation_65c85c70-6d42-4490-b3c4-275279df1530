'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  LayoutDashboard,
  User,
  Settings,
  BarChart3,
  FileText,
  Users,
  Bell,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  locale: string;
}

interface NavItem {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  labelKey: string;
}

const navItems: NavItem[] = [
  {
    href: '/dashboard',
    icon: LayoutDashboard,
    labelKey: 'dashboard',
  },
  {
    href: '/profile',
    icon: User,
    labelKey: 'profile',
  },
  {
    href: '/analytics',
    icon: BarChart3,
    labelKey: 'analytics',
  },
  {
    href: '/reports',
    icon: FileText,
    labelKey: 'reports',
  },
  {
    href: '/users',
    icon: Users,
    labelKey: 'users',
  },
  {
    href: '/notifications',
    icon: Bell,
    labelKey: 'notifications',
  },
  {
    href: '/settings',
    icon: Settings,
    labelKey: 'settings',
  },
  {
    href: '/help',
    icon: HelpCircle,
    labelKey: 'help',
  },
];

export function Sidebar({ locale }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const t = useTranslations('navigation');
  const tSidebar = useTranslations('sidebar');

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className={cn(
        'relative flex h-screen flex-col border-r bg-card transition-all duration-300',
        isCollapsed ? 'w-sidebar-collapsed' : 'w-sidebar'
      )}
    >
      {/* Header */}
      <div className="flex h-topbar items-center justify-between border-b px-4">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <LayoutDashboard className="h-5 w-5 text-primary-foreground" />
            </div>
            <span className="font-semibold text-lg">Dashboard</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="h-8 w-8"
          title={isCollapsed ? tSidebar('expand') : tSidebar('collapse')}
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-2">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === `/${locale}${item.href}`;

          return (
            <Link
              key={item.href}
              href={`/${locale}${item.href}`}
              className={cn(
                'flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground',
                isActive
                  ? 'bg-accent text-accent-foreground'
                  : 'text-muted-foreground',
                isCollapsed ? 'justify-center' : 'justify-start'
              )}
              title={isCollapsed ? t(item.labelKey) : undefined}
            >
              <Icon className={cn('h-4 w-4', !isCollapsed && 'mr-3')} />
              {!isCollapsed && <span>{t(item.labelKey)}</span>}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      {!isCollapsed && (
        <div className="border-t p-4">
          <div className="text-xs text-muted-foreground">
            © 2024 Dashboard
          </div>
        </div>
      )}
    </div>
  );
}
