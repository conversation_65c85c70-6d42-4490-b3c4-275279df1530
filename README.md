# Modern Dashboard with Firebase Authentication

A scalable Next.js dashboard application with Firebase authentication, multi-language support including RTL, theme management, and modern UI components.

## Features

- 🔐 **Firebase Authentication** - Email/password, Google, and Apple sign-in
- 🌍 **Multi-language Support** - English, Arabic, Spanish, French, German with RTL support
- 🎨 **Theme Management** - Light, dark, and auto modes with system preference detection
- 📱 **Responsive Design** - Mobile-first approach with Tailwind CSS
- 🔒 **Secure Token Management** - Automatic token refresh and secure storage
- 🚀 **Performance Optimized** - Code splitting, lazy loading, and image optimization
- ♿ **Accessibility** - WCAG compliant components
- 📊 **Dashboard Layout** - Collapsible sidebar and top navigation
- 🔧 **TypeScript** - Full type safety throughout the application

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Authentication**: Firebase Auth
- **Styling**: Tailwind CSS v4
- **UI Components**: Radix UI primitives
- **Internationalization**: next-intl
- **Form Handling**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **TypeScript**: Full type safety

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Firebase project

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd analyst_web
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure Firebase:
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Authentication with Email/Password, Google, and Apple providers
   - Copy your Firebase configuration to `.env.local`

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # Internationalized routes
│   │   ├── auth/          # Authentication pages
│   │   ├── dashboard/     # Dashboard pages
│   │   └── profile/       # User profile
├── components/            # Reusable components
│   ├── auth/             # Authentication components
│   ├── layout/           # Layout components
│   └── ui/               # UI primitives
├── contexts/             # React contexts
├── i18n/                 # Internationalization
├── lib/                  # Utilities and configurations
│   ├── api/              # API client
│   ├── auth/             # Authentication utilities
│   └── firebase/         # Firebase configuration
└── middleware.ts         # Next.js middleware
```

## Configuration

### Firebase Setup

1. Create a new Firebase project
2. Enable Authentication and configure providers:
   - Email/Password
   - Google
   - Apple (optional)
3. Add your domain to authorized domains
4. Copy configuration to environment variables

### Environment Variables

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# API Configuration
NEXT_PUBLIC_API_BASE_URL=your_api_url
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Features Overview

### Authentication
- Email/password registration and login
- Social authentication (Google, Apple)
- Secure token management with automatic refresh
- Protected routes and navigation guards

### Internationalization
- Support for 5 languages (en, ar, es, fr, de)
- RTL layout support for Arabic
- Dynamic language switching
- Localized routing

### Theme Management
- Light, dark, and auto themes
- System preference detection
- Persistent theme storage
- Smooth theme transitions

### UI Components
- Modern design system with Tailwind CSS
- Accessible components built with Radix UI
- Responsive layout with collapsible sidebar
- Form validation with React Hook Form and Zod

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
