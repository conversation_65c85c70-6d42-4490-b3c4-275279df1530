'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Search, Bell, User, LogOut, Settings, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { useAuth } from '@/contexts/auth-context';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface TopbarProps {
  locale: string;
}

export function Topbar({ locale }: TopbarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { user, signOut } = useAuth();
  const router = useRouter();
  const t = useTranslations('common');
  const tNav = useTranslations('navigation');
  const tLang = useTranslations('language');

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push(`/${locale}/auth`);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality
    console.log('Search query:', searchQuery);
  };

  const getInitials = (name: string | null) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-topbar items-center justify-between px-4">
        {/* Search */}
        <div className="flex flex-1 items-center space-x-4">
          <form onSubmit={handleSearch} className="relative max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder={t('search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4"
            />
          </form>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          {/* Language Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <Globe className="h-4 w-4" />
                <span className="sr-only">{tLang('language')}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{tLang('language')}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => router.push(`/en${window.location.pathname.slice(3)}`)}
                className={locale === 'en' ? 'bg-accent' : ''}
              >
                {tLang('english')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push(`/ar${window.location.pathname.slice(3)}`)}
                className={locale === 'ar' ? 'bg-accent' : ''}
              >
                {tLang('arabic')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push(`/es${window.location.pathname.slice(3)}`)}
                className={locale === 'es' ? 'bg-accent' : ''}
              >
                {tLang('spanish')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push(`/fr${window.location.pathname.slice(3)}`)}
                className={locale === 'fr' ? 'bg-accent' : ''}
              >
                {tLang('french')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push(`/de${window.location.pathname.slice(3)}`)}
                className={locale === 'de' ? 'bg-accent' : ''}
              >
                {tLang('german')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Notifications */}
          <Button variant="ghost" size="icon" className="h-9 w-9">
            <Bell className="h-4 w-4" />
            <span className="sr-only">{tNav('notifications')}</span>
          </Button>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={user?.photoURL || ''} alt={user?.displayName || ''} />
                  <AvatarFallback>
                    {getInitials(user?.displayName)}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.displayName || 'User'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => router.push(`/${locale}/profile`)}
              >
                <User className="mr-2 h-4 w-4" />
                {tNav('profile')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push(`/${locale}/settings`)}
              >
                <Settings className="mr-2 h-4 w-4" />
                {tNav('settings')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                {t('logout')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
