'use client';

import { useTranslations } from 'next-intl';
import { BarChart3, Users, TrendingUp, Activity } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';

interface DashboardPageProps {
  params: {
    locale: string;
  };
}

export default function DashboardPage({ params }: DashboardPageProps) {
  const { locale } = params;
  const { user } = useAuth();
  const t = useTranslations('common');
  const tNav = useTranslations('navigation');

  // Mock data for dashboard cards
  const stats = [
    {
      title: 'Total Users',
      value: '2,543',
      change: '+12%',
      icon: Users,
      trend: 'up',
    },
    {
      title: 'Revenue',
      value: '$45,231',
      change: '+8%',
      icon: TrendingUp,
      trend: 'up',
    },
    {
      title: 'Analytics',
      value: '12,234',
      change: '-3%',
      icon: BarChart3,
      trend: 'down',
    },
    {
      title: 'Activity',
      value: '573',
      change: '+23%',
      icon: Activity,
      trend: 'up',
    },
  ];

  return (
    <DashboardLayout locale={locale}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome back, {user?.displayName?.split(' ')[0] || 'User'}!
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your dashboard today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {stat.title}
                  </CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className={`text-xs ${
                    stat.trend === 'up' 
                      ? 'text-green-600 dark:text-green-400' 
                      : 'text-red-600 dark:text-red-400'
                  }`}>
                    {stat.change} from last month
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Recent Activity */}
          <Card className="col-span-2">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest activities and updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    action: 'User profile updated',
                    time: '2 minutes ago',
                    type: 'profile',
                  },
                  {
                    action: 'New report generated',
                    time: '1 hour ago',
                    type: 'report',
                  },
                  {
                    action: 'Settings modified',
                    time: '3 hours ago',
                    type: 'settings',
                  },
                  {
                    action: 'Dashboard accessed',
                    time: '5 hours ago',
                    type: 'access',
                  },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <div className="h-2 w-2 rounded-full bg-primary" />
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {activity.action}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <button className="w-full rounded-lg border border-dashed border-muted-foreground/25 p-4 text-left hover:bg-accent hover:text-accent-foreground">
                <div className="font-medium">Create Report</div>
                <div className="text-sm text-muted-foreground">
                  Generate a new analytics report
                </div>
              </button>
              <button className="w-full rounded-lg border border-dashed border-muted-foreground/25 p-4 text-left hover:bg-accent hover:text-accent-foreground">
                <div className="font-medium">Manage Users</div>
                <div className="text-sm text-muted-foreground">
                  Add or modify user accounts
                </div>
              </button>
              <button className="w-full rounded-lg border border-dashed border-muted-foreground/25 p-4 text-left hover:bg-accent hover:text-accent-foreground">
                <div className="font-medium">View Analytics</div>
                <div className="text-sm text-muted-foreground">
                  Check performance metrics
                </div>
              </button>
            </CardContent>
          </Card>
        </div>

        {/* Chart Section */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
            <CardDescription>
              Monthly performance metrics and trends
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Chart component would go here
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Integration with charting library like Chart.js or Recharts
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
